<template>
  <div class="ma-content-block p-4">
    <!-- 订单表格组件 -->
    <OrderTable
      ref="orderTableRef"
      @view-detail="handleViewDetail"
      @order-template="handleOrderTemplate"
      @change-purchaser="handleChangePurchaser"
      @purchase-remark="handlePurchaseRemark"
      @split-product="handleSplitProduct"
      @expense-order="handleExpenseOrder"
      @request="handleRequest"
      @supplier-updated="handleSupplierUpdated"
      @tax-classification-updated="handleTaxClassificationUpdated"
      @loss-reason-updated="handleLossReasonUpdated"
      @cost-total-updated="handleCostTotalUpdated"
      @product-cost-total-updated="handleProductCostTotalUpdated"
      @product-loss-reason-updated="handleProductLossReasonUpdated"
      @purchase-progress-updated="handlePurchaseProgressUpdated"
      @delivery-time-updated="handleDeliveryTimeUpdated"
      @generate-link="handleGenerateLink"
      @abolish-operation="handleAbolishOperation"
      @abolish-record="handleAbolishRecord"
      @view-product-detail="handleViewProductDetail"
    />

    <!-- 订单详情抽屉 -->
    <OrderDetailDrawer
      v-model="detailVisible"
      :order-id="currentOrderId"
      @edit="handleEditOrder"
      @action="handleOrderAction"
    />

    <!-- 下单模版弹窗 -->
    <OrderTemplateModal
      v-model="templateVisible"
      :order-data="currentRecord"
      @submit="handleTemplateSubmit"
    />

    <!-- 更换采购员弹窗 -->
    <ChangePurchaserModal
      v-model="purchaserVisible"
      :order-data="currentRecord"
      @submit="handlePurchaserSubmit"
    />

    <!-- 采购备注弹窗 -->
    <PurchaseRemarkModal
      v-model="remarkVisible"
      :order-data="currentRecord"
      @submit="handleRemarkSubmit"
    />

    <!-- 拆分商品抽屉 -->
    <SplitProductModal
      v-model="splitVisible"
      :product-data="currentSplitProduct"
      :order-data="currentRecord"
      @submit="handleSplitSubmit"
    />

    <!-- 费用单弹窗 -->
    <ExpenseOrderModal
      v-model="expenseVisible"
      :order-data="currentRecord"
      @submit="handleExpenseSubmit"
    />

    <!-- 费用单列表弹窗 -->
    <ExpenseOrderListModal
      v-model="expenseListVisible"
      :order-data="currentRecord"
      @refresh="refreshTable"
    />

    <!-- 商品详情抽屉 -->
    <ProductDetailDrawer
      v-model:visible="productDetailVisible"
      :product-id="currentProductId"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { Message } from "@arco-design/web-vue";

// 导入API
import { purchaseOrderApi } from "~/api/master/csm/purchaseOrder.js";
import { expenseOrderApi } from "~/api/master/csm/expenseOrder.js";

// 导入组件
import OrderTable from "./components/OrderTable/index.vue";
import OrderDetailDrawer from "./components/OrderDetailDrawer/index.vue";
import OrderTemplateModal from "./components/OrderTemplateModal/index.vue";
import ChangePurchaserModal from "./components/ChangePurchaserModal/index.vue";
import PurchaseRemarkModal from "./components/PurchaseRemarkModal/index.vue";
import SplitProductModal from "./components/SplitProductModal/index.vue";
import ExpenseOrderModal from "./components/ExpenseOrderModal/index.vue";
import ExpenseOrderListModal from "./components/ExpenseOrderListModal/index.vue";
import ProductDetailDrawer from "~/components/base/ProductDetailDrawer/index.vue";

// 路由实例
const router = useRouter();

// 页面状态
const loading = ref(false);
const tableData = ref([]);
const pageInfo = ref({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  totalPage: 1,
});

// 组件引用
const orderTableRef = ref(null);

// 刷新表格数据
const refreshTable = () => {
  console.log("主页面: 开始刷新表格数据...");
  console.log("主页面: orderTableRef.value:", orderTableRef.value);

  if (orderTableRef.value) {
    // 优先使用 OrderTable 组件暴露的 refreshTable 方法
    if (typeof orderTableRef.value.refreshTable === "function") {
      console.log("主页面: 调用 OrderTable 的 refreshTable 方法");
      orderTableRef.value.refreshTable();
    } else if (orderTableRef.value.crudRef) {
      console.log("主页面: 直接调用 crudRef 的刷新方法");
      const crudRef = orderTableRef.value.crudRef;

      if (typeof crudRef.refresh === "function") {
        crudRef.refresh();
        console.log("主页面: crudRef.refresh 调用成功");
      } else if (typeof crudRef.requestData === "function") {
        crudRef.requestData();
        console.log("主页面: crudRef.requestData 调用成功");
      } else {
        console.log("主页面: 未找到合适的刷新方法");
        console.log("主页面: crudRef 可用方法:", Object.keys(crudRef));
      }
    } else {
      console.log("主页面: OrderTable 组件未暴露刷新方法");
    }
  } else {
    console.log("主页面: orderTableRef 不存在");
  }
};

// 定义页面路由元信息
definePageMeta({
  name: "purchaseOrder",
  path: "/master/supply/purchaseOrder",
});

// 弹窗控制
const detailVisible = ref(false);
const templateVisible = ref(false);
const purchaserVisible = ref(false);
const remarkVisible = ref(false);
const splitVisible = ref(false);
const expenseVisible = ref(false);
const expenseListVisible = ref(false);
const productDetailVisible = ref(false);

// 当前操作的记录
const currentRecord = ref(null);
const currentOrderId = ref(null);
const currentSplitProduct = ref(null);
const currentProductId = ref(null);

// 初始化时检查并修正链接生成状态
const initializeLinkStatus = () => {
  tableData.value.forEach((order) => {
    const hasAllTax = checkAllTaxClassifications(order);
    if (!hasAllTax) {
      order.linkGenerated = false;
      order.linkStatus = "unlinked";
    }
  });
};

// 在组件挂载时执行初始化检查
onMounted(() => {
  initializeLinkStatus();
  // ma-crud会自动调用api加载数据，不需要手动调用
});

// API请求函数
const fetchPurchaseOrders = async (params = {}) => {
  try {
    loading.value = true;

    // 构建查询参数
    const queryParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
    };

    // tabs切换的采购状态参数
    if (params.purchaseStatus !== undefined && params.purchaseStatus !== "") {
      queryParams.purchaseStatus = params.purchaseStatus;
    }

    // 搜索条件 - 根据后端API参数进行映射
    if (params.purchaseOrderNumber)
      queryParams.purchaseOrderNumber = params.purchaseOrderNumber;
    if (params.originalOrderNumber)
      queryParams.originalOrderNumber = params.originalOrderNumber;
    if (params.channelId) queryParams.channelId = params.channelId;
    if (params.follower) queryParams.follower = params.follower;
    if (params.purchaser) queryParams.purchaser = params.purchaser;
    if (params.orderStatus) queryParams.orderStatus = params.orderStatus;
    if (params.auditStatus) queryParams.auditStatus = params.auditStatus;
    if (params.erpStatus) queryParams.erpStatus = params.erpStatus;
    if (params.linkStatus) queryParams.linkStatus = params.linkStatus;
    if (params.orderSource) queryParams.orderSource = params.orderSource;
    if (params.orderType) queryParams.orderType = params.orderType;
    if (params.buyerAccount) queryParams.buyerAccount = params.buyerAccount;
    if (params.supplierName) queryParams.supplierName = params.supplierName;
    if (params.logisticsNumber)
      queryParams.logisticsNumber = params.logisticsNumber;
    if (params.orderAddress) queryParams.orderAddress = params.orderAddress;
    if (params.purchaseRemark)
      queryParams.purchaseRemark = params.purchaseRemark;
    if (params.isLoss) queryParams.isLoss = params.isLoss;
    if (params.logisticsStatus) queryParams.logisticsStatus = params.logisticsStatus;

    // 商品相关搜索参数
    if (params.productCode) queryParams.productCode = params.productCode;
    if (params.productName) queryParams.productName = params.productName;
    if (params.recipientName) queryParams.recipientName = params.recipientName;
    if (params.contactPhone) queryParams.contactPhone = params.contactPhone;

    // 成本价范围参数 - 处理多种可能的参数格式
    if (
      params.costPrice &&
      Array.isArray(params.costPrice) &&
      params.costPrice.length === 2
    ) {
      queryParams.costPrice = params.costPrice;
    } else if (
      params.costPriceMin !== undefined ||
      params.costPriceMax !== undefined
    ) {
      // 处理分离的最小值和最大值参数
      const minValue = params.costPriceMin || 0;
      const maxValue = params.costPriceMax || 999999;
      queryParams.costPrice = [minValue, maxValue];
    } else if (
      params["costPrice[0]"] !== undefined ||
      params["costPrice[1]"] !== undefined
    ) {
      // 处理数组索引格式的参数
      const minValue = params["costPrice[0]"] || 0;
      const maxValue = params["costPrice[1]"] || 999999;
      queryParams.costPrice = [minValue, maxValue];
    }

    // 调试日志：打印成本价相关参数
    console.log("成本价相关参数:", {
      costPrice: params.costPrice,
      costPriceMin: params.costPriceMin,
      costPriceMax: params.costPriceMax,
      "costPrice[0]": params["costPrice[0]"],
      "costPrice[1]": params["costPrice[1]"],
      finalCostPrice: queryParams.costPrice,
    });

    // 时间范围参数
    if (params.purchaseTimeRange && Array.isArray(params.purchaseTimeRange)) {
      queryParams.purchaseTimeRange = params.purchaseTimeRange;
    }
    if (params.orderTimeRange && Array.isArray(params.orderTimeRange)) {
      queryParams.orderTimeRange = params.orderTimeRange;
    }

    const response = await purchaseOrderApi.getPurchaseOrders(queryParams);

    if (response.code === 200) {
      // 更新本地状态（用于其他操作）
      tableData.value = response.data.items || [];
      pageInfo.value = response.data.pageInfo || {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        totalPage: 1,
      };
      // 返回完整的响应给ma-crud
      return response;
    } else {
      throw new Error(response.message || "获取数据失败");
    }
  } catch (error) {
    console.error("获取采购订单列表失败:", error);
    Message.error(error.message || "获取数据失败");
    tableData.value = [];
    return {
      code: 500,
      message: error.message || "获取数据失败",
      data: {
        items: [],
        pageInfo: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          totalPage: 1,
        },
      },
    };
  } finally {
    loading.value = false;
  }
};

// 请求处理
const handleRequest = async (params, resolve) => {
  console.log("前端接收到的完整请求参数:", params);
  console.log("参数中包含的所有键:", Object.keys(params));
  try {
    const result = await fetchPurchaseOrders(params);
    // 如果有resolve回调，说明是ma-crud的api调用
    if (resolve) {
      resolve(result);
    }
    return result;
  } catch (error) {
    console.error("请求失败:", error);
    if (resolve) {
      resolve({
        code: 500,
        message: "请求失败",
        data: {
          items: [],
          pageInfo: {
            total: 0,
            currentPage: 1,
            pageSize: 10,
            totalPage: 1,
          },
        },
      });
    }
    throw error;
  }
};

// 操作事件处理函数
const handleViewDetail = (record) => {
  // 打开详情抽屉
  currentRecord.value = record;
  currentOrderId.value = record.id;
  detailVisible.value = true;
};

const handleOrderTemplate = (record) => {
  currentRecord.value = record;
  templateVisible.value = true;
};

const handleChangePurchaser = (record) => {
  currentRecord.value = record;
  purchaserVisible.value = true;
};

const handlePurchaseRemark = (record) => {
  currentRecord.value = record;
  remarkVisible.value = true;
};

const handleSplitProduct = (record) => {
  currentRecord.value = record;

  // 如果订单有多个商品，选择第一个商品进行拆分
  // 在实际应用中，可能需要弹出商品选择对话框
  if (record.products && record.products.length > 0) {
    currentSplitProduct.value = record.products[0];
  } else {
    // 如果是单商品订单，直接使用订单数据
    currentSplitProduct.value = {
      id: record.id,
      productName: record.productName || "未知商品",
      productCode: record.productCode || record.orderNumber,
      sku: record.sku || record.orderNumber,
      specification: record.specification || "标准规格",
      productImage: record.productImage || "",
      quantity: record.totalQuantity || 1,
      unitPrice: record.unitPrice || "0.00",
    };
  }

  splitVisible.value = true;
};

const handleExpenseOrder = (record) => {
  currentRecord.value = record;

  // 调试信息：检查费用单字段
  console.log("费用单操作 - 订单数据:", record);
  console.log("hasExpenseOrders:", record.hasExpenseOrders);
  console.log("expenseOrderCount:", record.expenseOrderCount);

  // 通过 hasExpenseOrders 字段判断是否存在费用单
  if (record.hasExpenseOrders) {
    console.log("存在费用单，打开费用单列表");
    // 存在费用单，打开费用单列表
    expenseListVisible.value = true;
  } else {
    console.log("不存在费用单，打开创建费用单弹窗");
    // 不存在费用单，直接打开创建费用单弹窗
    expenseVisible.value = true;
  }
};

// 表单提交处理
const handleTemplateSubmit = (data) => {
  console.log("保存下单模版:", data);
  Message.success("下单模版保存成功");
};

const handlePurchaserSubmit = async (data) => {
  try {
    console.log("更换采购员:", data);

    const response = await purchaseOrderApi.changePurchaser(data.orderId, {
      newPurchaserId: data.newPurchaserId,
      newPurchaser: data.newPurchaser,
      changeReason: data.changeReason,
    });

    if (response.code === 200) {
      Message.success("采购员更换成功");

      // 更新本地数据
      const index = tableData.value.findIndex(
        (item) => item.id === data.orderId
      );
      if (index !== -1) {
        tableData.value[index].purchaser = data.newPurchaser;
      }

      // 刷新表格数据
      refreshTable();
    } else {
      Message.error(response.message || "更换采购员失败");
    }
  } catch (error) {
    console.error("更换采购员失败:", error);
    Message.error(error.message || "更换采购员失败");
  }
};

const handleRemarkSubmit = async (data) => {
  try {
    console.log("更新采购备注:", data);

    const response = await purchaseOrderApi.updatePurchaseRemark(data.orderId, {
      newRemark: data.newRemark,
      remarkType: data.remarkType,
    });

    if (response.code === 200) {
      Message.success("采购备注更新成功");

      // 更新本地数据
      const index = tableData.value.findIndex(
        (item) => item.id === data.orderId
      );
      if (index !== -1) {
        tableData.value[index].purchaseRemark = response.data.purchase_remark;
      }

      // 刷新表格数据
      refreshTable();
    } else {
      Message.error(response.message || "更新采购备注失败");
    }
  } catch (error) {
    console.error("更新采购备注失败:", error);
    Message.error(error.message || "更新采购备注失败");
  }
};

const handleSplitSubmit = (data) => {
  console.log("拆分商品数据:", data);

  if (data.type === "multi-product-split") {
    // 处理多商品拆分结果
    console.log("多商品拆分结果:");
    data.splitResults.forEach((result, index) => {
      console.log(`商品${index + 1}: ${result.originalProduct.productName}`);
      console.log(`  - 总数量: ${result.originalProduct.quantity}`);
      console.log(`  - 已分配: ${result.totalAllocated}`);
      console.log(`  - 剩余: ${result.remainingQuantity}`);
      console.log(
        `  - 拆分项:`,
        result.splitItems
          .map((item) => `${item.splitGroupName}: ${item.quantity}`)
          .join(", ")
      );
    });

    // 这里可以调用后端API保存拆分结果
    // await saveSplitResults(data)

    Message.success(
      `成功拆分${
        data.splitResults.length
      }个商品，共生成${data.splitResults.reduce(
        (sum, r) => sum + r.splitItems.length,
        0
      )}个拆分项`
    );
  } else if (data.type === "single-product-split") {
    // 处理单商品拆分结果
    console.log("单商品拆分结果:");
    console.log(`原商品: ${data.originalProduct.productName}`);
    console.log(
      `拆分项:`,
      data.splitItems
        .map((item) => `${item.remark}: ${item.quantity}`)
        .join(", ")
    );

    Message.success(`商品拆分成功，生成${data.splitItems.length}个拆分项`);
  }

  // 刷新表格数据
  refreshTable();
};

const handleExpenseSubmit = (data) => {
  console.log("创建费用单:", data);
  // 刷新采购订单列表数据
  refreshTable();

  // 备用方案：延迟刷新
  setTimeout(() => {
    console.log("延迟刷新执行");
    refreshTable();
  }, 500);
};

// 处理订单编辑
const handleEditOrder = (orderData) => {
  console.log("编辑订单:", orderData);
  Message.info("编辑功能开发中...");
};

// 处理订单操作
const handleOrderAction = (action, orderData) => {
  const actionMap = {
    template: "下单模版",
    purchaser: "更换采购员",
    remark: "采购备注",
    split: "拆分商品",
    expense: "费用单",
  };

  console.log(`执行操作: ${actionMap[action]}`, orderData);

  // 根据操作类型打开对应的弹窗
  switch (action) {
    case "template":
      handleOrderTemplate(orderData);
      break;
    case "purchaser":
      handleChangePurchaser(orderData);
      break;
    case "remark":
      handlePurchaseRemark(orderData);
      break;
    case "split":
      handleSplitProduct(orderData);
      break;
    case "expense":
      handleExpenseOrder(orderData);
      break;
    default:
      Message.info(`${actionMap[action]}功能开发中...`);
  }
};

// 处理供应商更新
const handleSupplierUpdated = async ({ orderData, supplier, quotationPrice }) => {
  try {
    console.log("供应商更新:", { orderData, supplier, quotationPrice });

    // 供应商设置必须是商品级别的
    if (!supplier.itemId) {
      Message.error("供应商设置必须指定具体的商品项");
      return;
    }

    const requestData = {
      supplierId: supplier.id,
      supplierName: supplier.name,
      supplierCode: supplier.code || "",
      itemId: supplier.itemId,
    };

    // 如果有报价金额，添加到请求数据中
    if (quotationPrice && quotationPrice > 0) {
      requestData.quotationPrice = quotationPrice;
    }

    const response = await purchaseOrderApi.setActualSupplier(orderData.id, requestData);

    if (response.code === 200) {
      // 更新本地数据
      const index = tableData.value.findIndex(
        (item) => item.id === orderData.id
      );
      if (index !== -1) {
        const order = tableData.value[index];

        // 更新对应商品项的供应商信息
        if (order.products && order.products.length > 0) {
          const productIndex = order.products.findIndex(
            (p) => p.id === supplier.itemId
          );
          if (productIndex !== -1) {
            order.products[productIndex].actualSupplier = supplier.name;
            order.products[productIndex].actualSupplierId = supplier.id;

            // 如果有报价金额，更新实际成本
            if (quotationPrice && quotationPrice > 0) {
              order.products[productIndex].actualCost = quotationPrice;
              order.products[productIndex].costTotal = (quotationPrice * (order.products[productIndex].quantity || 1)).toFixed(2);
            }
          }
        }
        // 如果是单商品订单
        else if (!order.products) {
          order.actualSupplier = supplier.name;
          order.actualSupplierId = supplier.id;

          // 如果有报价金额，更新实际成本
          if (quotationPrice && quotationPrice > 0) {
            order.actualCost = quotationPrice;
            order.costTotal = (quotationPrice * (order.quantity || 1)).toFixed(2);
          }
        }
      }

      // 刷新表格数据
      refreshTable();

      const successMessage = quotationPrice
        ? `供应商更新成功，实际成本已更新为 ${quotationPrice} 元`
        : "供应商更新成功";
      Message.success(successMessage);
    } else {
      Message.error(response.message || "供应商更新失败");
    }
  } catch (error) {
    console.error("供应商更新失败:", error);
    Message.error(error.message || "供应商更新失败");
  }
};

// 处理税收分类更新
const handleTaxClassificationUpdated = async ({
  productData,
  taxClassification,
  orderData,
}) => {
  try {
    console.log("税收分类更新成功:", {
      productData,
      taxClassification,
      orderData,
    });

    const response = await purchaseOrderApi.updateTaxClassification(
      orderData.id,
      {
        itemId: productData.id,
        taxCategory: taxClassification.code || taxClassification.label,
        taxClassificationCode: taxClassification.code,
        taxClassificationName: taxClassification.label,
      }
    );

    if (response.code === 200) {
      // 更新本地数据
      const orderIndex = tableData.value.findIndex(
        (item) => item.id === orderData.id
      );

      if (orderIndex !== -1) {
        const order = tableData.value[orderIndex];

        // 如果是多商品订单
        if (order.products && order.products.length > 0) {
          const productIndex = order.products.findIndex(
            (p) => p.id === productData.id
          );
          if (productIndex !== -1) {
            order.products[productIndex].taxClassification = taxClassification;
          }
        }
        // 如果是单商品订单
        else if (order.id === productData.id) {
          order.taxClassification = taxClassification;
        }

        // 根据后端返回的信息更新链接状态
        if (response.data.can_generate_link) {
          order.linkGenerated = true;
          order.linkStatus = "linked";
        } else {
          order.linkGenerated = false;
          order.linkStatus = "unlinked";
        }
      }

      // 刷新表格数据
      refreshTable();
      Message.success("税收分类更新成功");
    } else {
      Message.error(response.message || "税收分类更新失败");
    }
  } catch (error) {
    console.error("税收分类更新失败:", error);
    Message.error(error.message || "税收分类更新失败");
  }
};

// 检查订单中所有商品是否都已选择税收分类的辅助函数
const checkAllTaxClassifications = (order) => {
  if (!order) return false;

  // 如果是多商品订单
  if (
    order.products &&
    Array.isArray(order.products) &&
    order.products.length > 0
  ) {
    return order.products.every(
      (product) => product.taxCategory || product.taxClassification?.label
    );
  }

  // 如果是单商品订单
  return !!(order.taxCategory || order.taxClassification?.label);
};

// 处理亏损原因更新
const handleLossReasonUpdated = async ({ orderData, lossReason, lossType }) => {
  try {
    console.log("亏损原因更新:", { orderData, lossReason, lossType });

    const response = await purchaseOrderApi.saveLossReason(orderData.id, {
      lossReason: lossReason,
      lossType: lossType || "other",
    });

    if (response.code === 200) {
      // 更新本地数据
      const index = tableData.value.findIndex(
        (item) => item.id === orderData.id
      );
      if (index !== -1) {
        tableData.value[index].lossReason = response.data.loss_reason;
        tableData.value[index].isLoss =
          response.data.is_loss === 1 ? "yes" : "no";
      }

      // 刷新表格数据
      refreshTable();
      Message.success("亏损原因保存成功");
    } else {
      Message.error(response.message || "亏损原因保存失败");
    }
  } catch (error) {
    console.error("亏损原因保存失败:", error);
    Message.error(error.message || "亏损原因保存失败");
  }
};

// 处理成本总价更新
const handleCostTotalUpdated = async ({ orderData, costTotal }) => {
  try {
    console.log("成本总价更新成功:", { orderData, costTotal });

    const response = await purchaseOrderApi.setCostTotal(orderData.id, {
      costTotal: costTotal,
      costType: "manual",
    });

    if (response.code === 200) {
      // 更新本地数据
      const index = tableData.value.findIndex(
        (item) => item.id === orderData.id
      );
      if (index !== -1) {
        tableData.value[index].costTotal = response.data.purchase_cost;
        tableData.value[index].grossProfitRate =
          response.data.gross_profit_rate;
      }

      // 刷新表格数据
      refreshTable();
      Message.success("成本总价更新成功");
    } else {
      Message.error(response.message || "成本总价更新失败");
    }
  } catch (error) {
    console.error("成本总价更新失败:", error);
    Message.error(error.message || "成本总价更新失败");
  }
};

// 处理商品成本总价更新
const handleProductCostTotalUpdated = async ({
  productData,
  orderData,
  costTotal,
}) => {
  try {
    console.log("商品成本总价更新:", { productData, orderData, costTotal });

    const response = await purchaseOrderApi.setProductCostTotal(
      productData.id,
      {
        costTotal: costTotal,
      }
    );

    if (response.code === 200) {
      // 更新本地数据
      const orderIndex = tableData.value.findIndex(
        (item) => item.id === orderData.id
      );
      if (orderIndex !== -1) {
        const productIndex = tableData.value[orderIndex].products?.findIndex(
          (p) => p.id === productData.id
        );
        if (productIndex !== -1) {
          tableData.value[orderIndex].products[productIndex].costTotal =
            costTotal;
        }
      }

      // 刷新表格数据
      refreshTable();
      Message.success("商品成本总价更新成功");
    } else {
      Message.error(response.message || "商品成本总价更新失败");
    }
  } catch (error) {
    console.error("商品成本总价更新失败:", error);
    Message.error(error.message || "商品成本总价更新失败");
  }
};

// 处理商品亏损原因更新
const handleProductLossReasonUpdated = async ({
  productData,
  orderData,
  lossReason,
}) => {
  try {
    console.log("商品亏损原因更新:", { productData, orderData, lossReason });

    const response = await purchaseOrderApi.setProductLossReason(
      productData.id,
      {
        lossReason: lossReason,
      }
    );

    if (response.code === 200) {
      // 更新本地数据
      const orderIndex = tableData.value.findIndex(
        (item) => item.id === orderData.id
      );
      if (orderIndex !== -1) {
        const productIndex = tableData.value[orderIndex].products?.findIndex(
          (p) => p.id === productData.id
        );
        if (productIndex !== -1) {
          tableData.value[orderIndex].products[productIndex].lossReason =
            lossReason;
        }
      }

      // 刷新表格数据
      refreshTable();
      Message.success("商品亏损原因更新成功");
    } else {
      Message.error(response.message || "商品亏损原因更新失败");
    }
  } catch (error) {
    console.error("商品亏损原因更新失败:", error);
    Message.error(error.message || "商品亏损原因更新失败");
  }
};

// 处理采购进度更新
const handlePurchaseProgressUpdated = async ({
  orderData,
  purchaseProgress,
}) => {
  try {
    console.log("采购进度更新:", { orderData, purchaseProgress });

    const response = await purchaseOrderApi.updatePurchaseProgress(
      orderData.id,
      {
        progress: purchaseProgress,
        progressDescription: `采购进度更新为: ${purchaseProgress}`,
      }
    );

    if (response.code === 200) {
      // 更新本地数据
      const index = tableData.value.findIndex(
        (item) => item.id === orderData.id
      );
      if (index !== -1) {
        tableData.value[index].purchaseProgress = purchaseProgress;
        // 如果后端返回了更新的采购状态，也要更新
        if (response.data.purchase_status !== undefined) {
          tableData.value[index].purchaseStatus = response.data.purchase_status;
        }
      }

      // 刷新表格数据
      refreshTable();
      Message.success("采购进度更新成功");
    } else {
      Message.error(response.message || "采购进度更新失败");
    }
  } catch (error) {
    console.error("采购进度更新失败:", error);
    Message.error(error.message || "采购进度更新失败");
  }
};

// 处理货期时间更新
const handleDeliveryTimeUpdated = async ({ orderData, deliveryTime }) => {
  try {
    console.log("货期时间更新:", { orderData, deliveryTime });

    // 这里可以调用API保存货期时间，目前先更新本地数据
    const index = tableData.value.findIndex((item) => item.id === orderData.id);
    if (index !== -1) {
      tableData.value[index].deliveryTime = deliveryTime;
    }

    // 可以在这里添加API调用来保存货期时间
    // const response = await purchaseOrderApi.updateDeliveryTime(orderData.id, {
    //   deliveryTime: deliveryTime
    // })

    Message.success("货期时间更新成功");
  } catch (error) {
    console.error("货期时间更新失败:", error);
    Message.error(error.message || "货期时间更新失败");
  }
};

// 处理生成链接
const handleGenerateLink = (updatedOrder) => {
  console.log("生成链接成功:", updatedOrder);

  // 更新订单数据
  const index = tableData.value.findIndex(
    (item) => item.id === updatedOrder.id
  );
  if (index !== -1) {
    // 更新订单信息
    tableData.value[index] = {
      ...tableData.value[index],
      ...updatedOrder,
      linkStatus: "linked", // 更新链接状态用于搜索筛选
      linkGenerated: true, // 标记链接已生成
    };
  }

  console.log("订单链接状态已更新:", tableData.value[index]);

  // 刷新表格数据
  refreshTable();
};

// 处理废止操作
const handleAbolishOperation = (record) => {
  console.log("废止操作:", record);
  Message.info("废止操作弹窗已打开");
};

// 处理废止记录
const handleAbolishRecord = (record) => {
  console.log("废止记录:", record);
  Message.info("废止记录弹窗已打开");
};

// 处理查看商品详情
const handleViewProductDetail = (productId) => {
  console.log("查看商品详情:", productId);
  currentProductId.value = productId;
  productDetailVisible.value = true;
};
</script>

<style scoped>
.ma-content-block {
  background-color: #ffffff;
  border-radius: var(--border-radius-medium);
  height: calc(100vh - 120px); /* 为固定表格布局提供合适的高度 */
  display: flex;
  flex-direction: column;
}

/* 确保OrderTable组件能够占用剩余空间 */
:deep(.order-table) {
  flex: 1;
  min-height: 0; /* 允许flex子项缩小 */
}
</style>
