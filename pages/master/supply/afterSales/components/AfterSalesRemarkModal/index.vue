<template>
  <a-modal
    v-model:visible="visible"
    title="售后备注"
    width="600px"
    :ok-loading="submitLoading"
    ok-text="保存备注"
    cancel-text="取消"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="售后编号" field="afterSalesNumber">
        <a-input v-model="formData.afterSalesNumber" readonly />
      </a-form-item>
      
      <!-- <a-form-item label="当前备注" field="currentRemark">
        <a-textarea 
          v-model="formData.currentRemark" 
          readonly
          :rows="3"
          placeholder="暂无备注"
        />
      </a-form-item> -->
      
      <a-form-item label="售后备注" field="newRemark" required>
        <a-textarea
          v-model="formData.newRemark"
          placeholder="请输入新的备注内容"
          :rows="4"
        />
      </a-form-item>

      <a-form-item label="备注附件" field="remarkAttachments">
        <ma-upload
          v-model="formData.remarkAttachments"
          type="file"
          :multiple="true"
          :limit="10"
          accept=".doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.pdf,.txt"
          :draggable="true"
          :size="10 * 1024 * 1024"
          tip="支持格式：doc、docx、xls、xlsx、jpg、jpeg、png、pdf、txt等，单个文件不超过10MB"
          return-type="url"
          :request-data="{
            dir: 'after-sales',
            module: 'supply',
            bizType: 'after-sales-remark',
            isPublic: 'true'
          }"
        />
      </a-form-item>

      <!-- 显示已有附件 -->
      <a-form-item
        v-if="existingAttachments.length > 0"
        label="已有附件"
        field="existingAttachments"
      >
        <div class="existing-attachments">
          <div
            v-for="(attachment, index) in existingAttachments"
            :key="index"
            class="attachment-item"
          >
            <div class="attachment-info">
              <icon-file class="file-icon" />
              <span class="file-name">{{ getFileName(attachment) }}</span>
              <span class="file-size">{{ getFileSize(attachment) }}</span>
            </div>
            <div class="attachment-actions">
              <a-button
                type="text"
                size="small"
                @click="handlePreviewAttachment(attachment)"
              >
                <template #icon><icon-eye /></template>
                预览
              </a-button>
              <a-button
                type="text"
                size="small"
                @click="handleDownloadAttachment(attachment)"
              >
                <template #icon><icon-download /></template>
                下载
              </a-button>
            </div>
          </div>
        </div>
      </a-form-item>
      <div>支持的文件类型:doc,xlsx,xis,jpg..png..pdf,最多只能上传10个文件，大小不相超过10M</div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import MaUpload from '@/components/base/ma-upload/index.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  afterSalesData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const formRef = ref()
const visible = ref(false)
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
  afterSalesNumber: '',
  currentRemark: '',
  newRemark: '',
  remarkAttachments: []
})

// 已有附件
const existingAttachments = ref([])

// 表单验证规则
const rules = {
  newRemark: [
    { required: true, message: '请输入备注内容' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.afterSalesData && typeof props.afterSalesData === 'object') {
    console.log( props.afterSalesData,' props.afterSalesData');
    // 填充表单数据
    formData.afterSalesNumber = props.afterSalesData.afterSalesNumber || ''
    formData.currentRemark = props.afterSalesData.remark || ''
    formData.newRemark =props.afterSalesData.remark || ''
    formData.remarkAttachments = []

    // 设置已有附件 - 从 attachments 数组中筛选出备注附件
    if (props.afterSalesData.attachments && Array.isArray(props.afterSalesData.attachments)) {
      existingAttachments.value = props.afterSalesData.attachments.filter(
        attachment => attachment.attachmentType === 'remark'
      )
      console.log('筛选出的备注附件:', existingAttachments.value)
    } else {
      existingAttachments.value = []
    }
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})



// 获取文件名
const getFileName = (attachment) => {
  if (typeof attachment === 'string') {
    return attachment.split('/').pop() || attachment
  }
  // 优先使用 fileName，然后是 name，最后从 fileUrl 中提取
  return attachment.fileName || attachment.name ||
         (attachment.fileUrl ? attachment.fileUrl.split('/').pop() : '未知文件')
}

// 获取文件大小
const getFileSize = (attachment) => {
  if (typeof attachment === 'object') {
    // 优先使用 fileSize，然后是 size
    const sizeValue = attachment.fileSize || attachment.size
    if (sizeValue) {
      const size = parseInt(sizeValue)
      if (isNaN(size) || size === 0) return ''
      if (size < 1024) return `${size}B`
      if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
      return `${(size / (1024 * 1024)).toFixed(1)}MB`
    }
  }
  return ''
}

// 预览附件
const handlePreviewAttachment = (attachment) => {
  console.log('预览附件:', attachment)

  let url = ''
  if (typeof attachment === 'string') {
    url = attachment
  } else {
    // 优先使用 fileUrl，然后是 url，最后是 path
    url = attachment.fileUrl || attachment.url || attachment.path || ''
  }

  if (url) {
    // 判断文件类型
    const fileExtension = url.split('.').pop().toLowerCase()
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
    const pdfTypes = ['pdf']

    if (imageTypes.includes(fileExtension)) {
      // 图片预览
      window.open(url, '_blank')
    } else if (pdfTypes.includes(fileExtension)) {
      // PDF预览
      window.open(url, '_blank')
    } else {
      // 其他文件类型直接下载
      handleDownloadAttachment(attachment)
    }
  } else {
    Message.warning('无法预览此文件')
  }
}

// 下载附件
const handleDownloadAttachment = (attachment) => {
  console.log('下载附件:', attachment)

  let url = ''
  let fileName = ''

  if (typeof attachment === 'string') {
    url = attachment
    fileName = attachment.split('/').pop() || 'download'
  } else {
    // 优先使用 fileUrl，然后是 url，最后是 path
    url = attachment.fileUrl || attachment.url || attachment.path || ''
    // 优先使用 fileName，然后是 name，最后从 URL 中提取
    fileName = attachment.fileName || attachment.name ||
               (url ? url.split('/').pop() : 'download')
  }

  if (url) {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    Message.success('开始下载文件')
  } else {
    Message.error('无法下载此文件')
  }
}

// 自定义表单校验
const validateForm = () => {
  // 校验新增备注
  if (!formData.newRemark || !formData.newRemark.trim()) {
    Message.error('请输入备注内容')
    return false
  }

  if (formData.newRemark.trim().length > 1000) {
    Message.error('备注内容不能超过1000个字符')
    return false
  }

  // 校验附件
  const attachmentCount = Array.isArray(formData.remarkAttachments)
    ? formData.remarkAttachments.length
    : (formData.remarkAttachments ? 1 : 0)

  if (attachmentCount > 10) {
    Message.error('最多只能上传10个附件')
    return false
  }

  return true
}

// 提交处理
const handleSubmit = async () => {
  // 使用自定义校验
  if (!validateForm()) {
    return
  }

  try {
    submitLoading.value = true

    const submitData = {
      afterSalesId: props.afterSalesData.id,
      remark: formData.newRemark.trim(),
      // 处理附件数据，ma-upload 组件返回的是 URL 数组
      attachments: Array.isArray(formData.remarkAttachments)
        ? formData.remarkAttachments.map(url => ({
            name: url.split('/').pop() || 'attachment',
            url: url,
            size: 0,
            type: ''
          }))
        : (formData.remarkAttachments ? [{
            name: formData.remarkAttachments.split('/').pop() || 'attachment',
            url: formData.remarkAttachments,
            size: 0,
            type: ''
          }] : [])
    }

    emit('submit', submitData)

    // 不在这里显示成功消息，由父组件处理
    handleCancel()
  } catch (error) {
    console.error('提交售后备注失败:', error)
    Message.error('提交失败，请稍后重试')
  } finally {
    submitLoading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  visible.value = false
  formRef.value?.resetFields()
  // 重置附件数据
  formData.remarkAttachments = []
  existingAttachments.value = []
}
</script>

<style scoped>
:deep(.arco-form-item-label) {
  font-weight: 500;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.existing-attachments {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 12px;
  background-color: #fafafa;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e6eb;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.file-icon {
  color: #666;
  font-size: 16px;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.attachment-actions .arco-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .attachment-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
